-- libgnss_gps.lua - Simple GPS module using libgnss for Air780EG
-- This module provides basic GPS functionality using the libgnss library

-- Required modules
local sys = require("sys")

local GPS = {
    uart_id = 1,
    baudrate = 9600,
    satellites = 0,
    fix_quality = 0,
    hdop = 0,
    initialized = false,
    position = nil
}

-- Initialize GPS
function GPS.init()
    if not libgnss then return false end

    -- Try different power methods
    if pm and pm.GPS then
        pm.power(pm.GPS, true)
    elseif pmd and pmd.ldoset then
        pmd.ldoset(1, pmd.LDO_VIBR)
    end

    sys.wait(1000)
    libgnss.clear()
    uart.setup(GPS.uart_id, GPS.baudrate)
    libgnss.bind(GPS.uart_id)

    GPS.initialized = true
    return true
end

-- Get current position
function GPS.getPosition()
    if not GPS.initialized or not libgnss.isFix() then
        local gga = libgnss.getGga()
        if gga then
            GPS.satellites = gga.satellites or 0
            GPS.hdop = gga.hdop or 0
            GPS.fix_quality = gga.quality or 0
        end
        return nil
    end

    local rmcData = libgnss.getRmc()
    if rmcData then
        local speed_raw = rmcData.speed and rmcData.speed * 1.852 or 0
        local speed_kmh = speed_raw > 5.0 and speed_raw or 0

        local gga = libgnss.getGga()
        if gga then
            GPS.satellites = gga.satellites or 0
            GPS.hdop = gga.hdop or 0
            GPS.fix_quality = gga.quality or 0
        end

        return {
            latitude = rmcData.lat,
            longitude = rmcData.lng,
            speed = speed_kmh,
            satellites = GPS.satellites,
            fix_quality = GPS.fix_quality,
            hdop = GPS.hdop,
            timestamp = os.time(),
            source = "GPS"
        }
    end
    return nil
end

-- Get satellite information
function GPS.getSatelliteInfo()
    local gga = libgnss and libgnss.getGga and libgnss.getGga()
    if gga then
        GPS.satellites = gga.satellites or 0
        GPS.hdop = gga.hdop or 0
        GPS.fix_quality = gga.quality or 0
    end
    return {
        satellites = GPS.satellites,
        fix_quality = GPS.fix_quality,
        hdop = GPS.hdop,
        has_fix = libgnss and libgnss.isFix() or false
    }
end

function GPS.hasFix()
    return libgnss and libgnss.isFix() or false
end

return GPS
