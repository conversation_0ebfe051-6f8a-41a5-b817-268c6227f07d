-- libgnss_gps.lua - Simple GPS module using libgnss for Air780EG
-- This module provides basic GPS functionality using the libgnss library

-- Required modules
local sys = require("sys")

local GPS = {
    uart_id = 2,
    baudrate = 115200,
    satellites = 0,
    fix_quality = 0,
    hdop = 0,
    initialized = false,
    position = nil
}

-- Initialize GPS using proper Air780EG method
function GPS.init()
    if not libgnss then return false end

    pm.power(pm.GPS, true)  -- Turn on GPS power
    libgnss.clear()         -- Initialize GPS library
    uart.setup(2, 115200)   -- Initialize UART2 at 115200 baud
    libgnss.bind(2)         -- Bind GPS library to UART2
    libgnss.debug(true)     -- Enable GPS debug output

    -- Subscribe to GPS state events
    sys.subscribe("GNSS_STATE", function(event, ticks)
        print("GPS state:", event, ticks)
    end)

    GPS.initialized = true
    print("GPS initialized on UART2")
    return true
end

-- Convert NMEA coordinate format to decimal degrees
local function convertCoordinate(coord)
    if not coord or coord == 0 then return 0 end

    -- NMEA format: DDMM.MMMM (degrees + minutes)
    local degrees = math.floor(coord / 100)
    local minutes = coord - (degrees * 100)

    return degrees + (minutes / 60)
end

-- Get current position using Air780EG method
function GPS.getPosition()
    if not GPS.initialized then
        print("GPS not initialized")
        return nil
    end

    -- Update satellite info first
    local gga = libgnss.getGga()
    if gga then
        GPS.satellites = gga.satellites or 0
        GPS.hdop = gga.hdop or 0
        GPS.fix_quality = gga.quality or 0
    end

    print("GPS status - Fix:", libgnss.isFix(), "Satellites:", GPS.satellites, "Quality:", GPS.fix_quality)

    -- Check if we have a GPS fix
    if libgnss.isFix() then
        local rmcData = libgnss.getRmc(1)  -- Get RMC data with parameter
        if rmcData and rmcData.lng and rmcData.lat then
            print("Raw GPS data - lat:", rmcData.lat, "lng:", rmcData.lng)

            -- Calculate the exact conversion factor from the known NMEA data
            -- NMEA shows 4754.23069, libgnss gives 479038440
            -- So the factor is: 479038440 / 4754.23069 ≈ 100,000 (approximately)
            -- Let's use direct conversion: divide by 100,000 then apply NMEA conversion
            local lat_nmea = rmcData.lat / 100000
            local lng_nmea = rmcData.lng / 100000
            print("NMEA format - lat:", lat_nmea, "lng:", lng_nmea)

            -- Convert from NMEA format to decimal degrees
            local lat_decimal = convertCoordinate(lat_nmea)
            local lng_decimal = convertCoordinate(lng_nmea)
            print("Decimal degrees - lat:", lat_decimal, "lng:", lng_decimal)

            local speed_raw = rmcData.speed and rmcData.speed * 1.852 or 0
            local speed_kmh = speed_raw > 5.0 and speed_raw or 0

            GPS.position = {
                latitude = lat_decimal,
                longitude = lng_decimal,
                speed = speed_kmh,
                satellites = GPS.satellites,
                fix_quality = GPS.fix_quality,
                hdop = GPS.hdop,
                timestamp = os.time(),
                source = "GPS"
            }
            return GPS.position
        end
    else
        print("No GPS fix available")
        -- Clear cached position when no fix
        GPS.position = nil
    end
    return nil
end

-- Get satellite information
function GPS.getSatelliteInfo()
    local gga = libgnss and libgnss.getGga and libgnss.getGga()
    if gga then
        GPS.satellites = gga.satellites or 0
        GPS.hdop = gga.hdop or 0
        GPS.fix_quality = gga.quality or 0
    end
    return {
        satellites = GPS.satellites,
        fix_quality = GPS.fix_quality,
        hdop = GPS.hdop,
        has_fix = libgnss and libgnss.isFix() or false
    }
end

function GPS.hasFix()
    return libgnss and libgnss.isFix() or false
end

return GPS
