-- libgnss_gps.lua - Simple GPS module using libgnss for Air780EG
-- This module provides basic GPS functionality using the libgnss library

-- Required modules
local sys = require("sys")

local GPS = {
    debug = true,           -- Enable debug output to troubleshoot GPS issues
    uart_id = 2,            -- UART ID for GPS
    baudrate = 115200,      -- Baud rate for GPS UART
    satellites = 0,         -- Number of satellites in view
    fix_quality = 0,        -- Fix quality (0=no fix, 1=GPS, 2=DGPS)
    hdop = 0,               -- Horizontal dilution of precision
    initialized = false,    -- Initialization state
    position = nil          -- Current position data
}

-- Helper function to log messages when debug is enabled
local function log(msg)
    if GPS.debug then
        print("[GPS] " .. msg)
    end
end

-- Initialize GPS
function GPS.init()
    log("Initializing GPS module using libgnss...")

    -- Power on GPS
    if pm and pm.GPS then
        local power_result = pm.power(pm.GPS, true)
        log("GPS power result: " .. tostring(power_result))
    else
        log("Warning: pm.GPS not available, using alternative method")
        -- Try alternative method for Air780EG
        if pmd and pmd.ldoset then
            pmd.ldoset(1, pmd.LDO_VIBR)  -- Turn on VIBR LDO for GPS
            log("Turned on VIBR LDO for GPS")
        end
    end

    -- Wait a bit for GPS to power up
    sys.wait(1000)

    -- Initialize libgnss
    if not libgnss then
        log("Error: libgnss module not available")
        return false
    end

    -- Clear any previous state
    libgnss.clear()

    -- Setup UART for GPS
    uart.setup(GPS.uart_id, GPS.baudrate)

    -- Bind libgnss to the UART
    libgnss.bind(GPS.uart_id)

    -- Enable debug output if needed
    if GPS.debug then
        libgnss.debug(true)
    end

    -- Subscribe to GPS state changes
    sys.subscribe("GNSS_STATE", function(event, ticks)
        log("GNSS state change: " .. event .. ", ticks: " .. ticks)
    end)

    GPS.initialized = true
    log("GPS initialized successfully using libgnss")
    return true
end

-- Get current position
function GPS.getPosition()
    if not GPS.initialized then
        log("GPS not initialized")
        return nil
    end

    -- Check if we have a GPS fix
    if libgnss.isFix() then
        -- Get RMC data (position, speed, etc.)
        local rmcData = libgnss.getRmc()
        if rmcData then
            -- Convert speed from knots to km/h (1 knot = 1.852 km/h)
            local speed_raw = rmcData.speed and rmcData.speed * 1.852 or 0

            -- Apply speed filtering: only report speed if > 5 km/h, otherwise 0
            local speed_kmh = speed_raw > 5.0 and speed_raw or 0

            -- Get satellite info
            local gga = libgnss.getGga()
            if gga then
                GPS.satellites = gga.satellites or 0
                GPS.hdop = gga.hdop or 0
                GPS.fix_quality = gga.quality or 0
            end

            -- Return position data
            return {
                latitude = rmcData.lat,
                longitude = rmcData.lng,
                speed = speed_kmh,
                satellites = GPS.satellites,
                fix_quality = GPS.fix_quality,
                hdop = GPS.hdop,
                timestamp = os.time(),
                source = "GPS"
            }
        end
    else
        -- No fix yet
        -- Try to get satellite info even without a fix
        local gga = libgnss.getGga()
        if gga then
            GPS.satellites = gga.satellites or 0
            GPS.hdop = gga.hdop or 0
            GPS.fix_quality = gga.quality or 0
        end

        log("No valid fix yet. Satellites: " .. GPS.satellites .. ", Fix quality: " .. GPS.fix_quality)
    end

    return nil
end

-- Get satellite information
function GPS.getSatelliteInfo()
    -- Try to get satellite info from GGA
    local gga = libgnss and libgnss.getGga and libgnss.getGga()
    if gga then
        GPS.satellites = gga.satellites or 0
        GPS.hdop = gga.hdop or 0
        GPS.fix_quality = gga.quality or 0
    end

    -- Try to get additional satellite info from GSA
    local gsa = libgnss and libgnss.getGsa and libgnss.getGsa()
    if gsa then
        -- GSA mode: 1=No fix, 2=2D fix, 3=3D fix
        if gsa.mode and tonumber(gsa.mode) > 1 and GPS.fix_quality == 0 then
            GPS.fix_quality = 1  -- If GSA shows a fix but GGA doesn't, trust GSA
        end

        -- Use HDOP from GSA if available and GGA didn't provide it
        if gsa.hdop and GPS.hdop == 0 then
            GPS.hdop = gsa.hdop
        end
    end

    -- Try to get satellite count from GSV
    -- This is more complex and would require parsing multiple GSV sentences
    -- We'll stick with the GGA satellite count for simplicity

    return {
        satellites = GPS.satellites,
        fix_quality = GPS.fix_quality,
        hdop = GPS.hdop,
        has_fix = libgnss and libgnss.isFix() or false
    }
end

-- Check if GPS has a fix
function GPS.hasFix()
    return libgnss and libgnss.isFix() or false
end

-- Get raw NMEA data for debugging
function GPS.getRawData()
    local data = {}

    -- Get RMC data (position, speed, etc.)
    if libgnss and libgnss.getRmc then
        data.rmc = libgnss.getRmc()
    end

    -- Get GGA data (fix data, altitude, etc.)
    if libgnss and libgnss.getGga then
        data.gga = libgnss.getGga()
    end

    -- Get GSA data (satellite data)
    if libgnss and libgnss.getGsa then
        data.gsa = libgnss.getGsa()
    end

    -- Add fix status
    data.hasFix = libgnss and libgnss.isFix() or false

    -- Add satellite info
    data.satellites = GPS.satellites
    data.fix_quality = GPS.fix_quality
    data.hdop = GPS.hdop

    return data
end

return GPS
