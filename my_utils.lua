-- my_utils.lua - Simplified utility functions for Air780EG
local my_utils = {}
local vars = require('variables')

function my_utils.fileExists(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end
    return false
end

function my_utils.readFile(path)
    local file = io.open(path, "r")
    if file then
        local content = file:read("*a")
        file:close()
        return content
    end
    return nil
end

function my_utils.createDirectory(path)
    -- For Air780EG, use the official io.mkdir method
    print("Creating directory:", path)

    -- Method 1: Use io.mkdir (Air780EG official method)
    if io and io.mkdir then
        local success, err = io.mkdir(path)
        if success then
            print("Directory created with io.mkdir:", path)
            return true
        else
            print("io.mkdir failed:", err or "unknown error")
        end
    end

    -- Method 2: Try rtos.make_dir as fallback
    if rtos and rtos.make_dir then
        local success, err = pcall(rtos.make_dir, path)
        if success then
            print("Directory created with rtos.make_dir:", path)
            return true
        else
            print("rtos.make_dir failed:", err)
        end
    end

    -- Method 3: Try to create a test file to verify directory exists/can be created
    local test_file = io.open(path .. "/test.tmp", "w")
    if test_file then
        test_file:close()
        os.remove(path .. "/test.tmp")
        print("Directory verified with test file:", path)
        return true
    end

    print("All directory creation methods failed for:", path)
    return false
end

function my_utils.writeToFile(path, content)
    print("Attempting to write to file:", path, "Content:", tostring(content))

    -- Ensure directory exists
    local dir = string.match(path, "(.+)/[^/]+$")
    if dir then
        print("Ensuring directory exists:", dir)
        local dir_created = my_utils.createDirectory(dir)
        if not dir_created then
            print("Warning: Directory creation failed, but attempting file write anyway")
        end
    end

    -- Use standard Air780EG io.open approach
    local file = io.open(path, "w")
    if file then
        file:write(tostring(content))
        file:close()
        print("Successfully wrote to file:", path)
        return true
    else
        print("Failed to open file for writing:", path)
        return false
    end
end

function my_utils.formatNumber(num, decimals)
    decimals = decimals or 2
    local mult = 10^decimals
    return math.floor(num * mult + 0.5) / mult
end

-- Load configuration from files
function my_utils.loadConfiguration()
    -- Load phone numbers from /data/ directory (Air780EG standard)
    if my_utils.fileExists("/data/phone1.txt") then
        vars.phone_number1 = my_utils.readFile("/data/phone1.txt")
    end
    if my_utils.fileExists("/data/phone2.txt") then
        vars.phone_number2 = my_utils.readFile("/data/phone2.txt")
    end
    if my_utils.fileExists("/data/phone3.txt") then
        vars.phone_number3 = my_utils.readFile("/data/phone3.txt")
    end

    -- Load voltage settings
    if my_utils.fileExists("/data/volt_offset.txt") then
        vars.voltage_offset = tonumber(my_utils.readFile("/data/volt_offset.txt")) or 0
    end
    if my_utils.fileExists("/data/volt_threshold.txt") then
        vars.voltage_threshold = tonumber(my_utils.readFile("/data/volt_threshold.txt")) or 0.5
    end
    if my_utils.fileExists("/data/volt_notify.txt") then
        vars.voltage_notify_flag = my_utils.readFile("/data/volt_notify.txt") == "true"
    else
        vars.voltage_notify_flag = false  -- Default to false
    end

    -- Load license setting
    if my_utils.fileExists("/data/license.txt") then
        vars.isLicensed = my_utils.readFile("/data/license.txt") == "true"
    else
        vars.isLicensed = true  -- Default to true
    end

    -- Load key state setting
    if my_utils.fileExists("/data/key_state.txt") then
        vars.key_state = my_utils.readFile("/data/key_state.txt") == "true"
    else
        vars.key_state = false  -- Default to false
    end

    -- Load sound flag setting
    if my_utils.fileExists("/data/sound_flag.txt") then
        vars.sound_flag = my_utils.readFile("/data/sound_flag.txt") == "true"
    else
        vars.sound_flag = true  -- Default to true
    end

    -- Load Geely Atlas mode setting
    if my_utils.fileExists("/data/geely_mode.txt") then
        vars.geely_atlas_mode = my_utils.readFile("/data/geely_mode.txt") == "true"
    else
        vars.geely_atlas_mode = false  -- Default to false
    end

    -- Load timing parameters
    if my_utils.fileExists("/data/lock_wait.txt") then
        vars.lock_wait_duration = tonumber(my_utils.readFile("/data/lock_wait.txt")) or 2000
    end
    if my_utils.fileExists("/data/lock_press.txt") then
        vars.lock_press_duration = tonumber(my_utils.readFile("/data/lock_press.txt")) or 1000
    end
    if my_utils.fileExists("/data/unlock_wait.txt") then
        vars.unlock_wait_duration = tonumber(my_utils.readFile("/data/unlock_wait.txt")) or 1000
    end
    if my_utils.fileExists("/data/unlock_press.txt") then
        vars.unlock_press_duration = tonumber(my_utils.readFile("/data/unlock_press.txt")) or 1000
    end
    if my_utils.fileExists("/data/mirror_duration.txt") then
        vars.mirror_duration = tonumber(my_utils.readFile("/data/mirror_duration.txt")) or 2000
    end

    print("Configuration loaded from persistent storage")
end

-- Save individual configuration settings
function my_utils.saveConfig(key, value)
    local file_map = {
        voltage_offset = "/data/volt_offset.txt",
        voltage_threshold = "/data/volt_threshold.txt",
        voltage_notify_flag = "/data/volt_notify.txt",
        key_state = "/data/key_state.txt",
        sound_flag = "/data/sound_flag.txt",
        geely_atlas_mode = "/data/geely_mode.txt",
        isLicensed = "/data/license.txt",
        lock_wait_duration = "/data/lock_wait.txt",
        lock_press_duration = "/data/lock_press.txt",
        unlock_wait_duration = "/data/unlock_wait.txt",
        unlock_press_duration = "/data/unlock_press.txt",
        mirror_duration = "/data/mirror_duration.txt"
    }

    local file_path = file_map[key]
    if file_path then
        local success = my_utils.writeToFile(file_path, tostring(value))
        if success then
            print("Saved config:", key, "=", value)
        else
            print("Failed to save config:", key)
        end
        return success
    else
        print("Unknown config key:", key)
        return false
    end
end

-- Save all current configuration to files
function my_utils.saveAllConfig()
    my_utils.saveConfig("voltage_offset", vars.voltage_offset)
    my_utils.saveConfig("voltage_threshold", vars.voltage_threshold)
    my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
    my_utils.saveConfig("key_state", vars.key_state)
    my_utils.saveConfig("sound_flag", vars.sound_flag)
    my_utils.saveConfig("geely_atlas_mode", vars.geely_atlas_mode)
    my_utils.saveConfig("isLicensed", vars.isLicensed)
    my_utils.saveConfig("lock_wait_duration", vars.lock_wait_duration)
    my_utils.saveConfig("lock_press_duration", vars.lock_press_duration)
    my_utils.saveConfig("unlock_wait_duration", vars.unlock_wait_duration)
    my_utils.saveConfig("unlock_press_duration", vars.unlock_press_duration)
    my_utils.saveConfig("mirror_duration", vars.mirror_duration)
    print("All configuration saved to persistent storage")
end

return my_utils
