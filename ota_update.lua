-- ota_update.lua - OTA update module for Air780EG using libfota2
-- This module provides OTA functionality using the official libfota2 API

local ota_update = {}

-- Load required modules
local sys = require("sys")

-- OTA state variables
local ota_in_progress = false
local ota_callback = nil

-- Try to load libfota2 module (official Air780EG FOTA library)
local libfota2_available = false
local libfota2 = nil

-- Check for libfota2 module
if pcall(require, "libfota2") then
    libfota2 = require("libfota2")
    libfota2_available = true
    print("libfota2 module loaded successfully")
else
    print("libfota2 module not available")
end

-- Function to get device IMEI
local function getIMEI()
    if mobile and mobile.imei then
        return mobile.imei()
    end
    return "unknown"
end

-- Function to get current firmware version info
local function getFirmwareInfo()
    local coreVer = "unknown_V0"

    -- Try different methods to get version info
    if rtos and rtos.get_version then
        coreVer = rtos.get_version()
    elseif _G.VERSION then
        coreVer = "LuatOS_V" .. _G.VERSION
    else
        coreVer = "LuatOS_V1"
    end

    local coreName1, coreName2 = coreVer:match("(.-)_V%d+(_.+)")
    local coreVersion = tonumber(coreVer:match(".-_V(%d+)"))

    local info = {
        core_name = (coreName1 or "LuatOS") .. (coreName2 or ""),
        core_version = coreVersion or 1,
        project = _G.PROJECT or "unknown",
        version = _G.VERSION or "1.0.0",
        product_key = _G.PRODUCT_KEY or ""
    }

    print("Firmware info:")
    print("- Core version:", coreVer)
    print("- Core name:", info.core_name)
    print("- Core version number:", info.core_version)
    print("- Project:", info.project)
    print("- Version:", info.version)
    print("- Product key:", info.product_key)

    return info
end



-- Progress callback wrapper for libfota
local function progress_wrapper(progress)
    if download_progress_callback then
        download_progress_callback(progress)
    end
    print("OTA download progress:", progress .. "%")
end

-- FOTA callback function for libfota2
local function libfota2_callback(result)
    print("libfota2 callback result:", result)

    -- Provide detailed error information based on libfota2 documentation
    local error_messages = {
        [0] = "Success - Update downloaded and verified",
        [1] = "Connection failed - Check network or server configuration",
        [2] = "URL error - Check URL spelling",
        [3] = "Server disconnected - Check server whitelist configuration",
        [4] = "Receive packet error - Check module firmware or upgrade package",
        [5] = "Version format error - IoT platform requires xxx.yyy.zzz format"
    }

    local error_msg = error_messages[result] or ("Unknown error code: " .. tostring(result))
    print("OTA result details:", error_msg)

    ota_in_progress = false

    if ota_callback then
        if result == 0 then
            ota_callback(true)  -- Success
        else
            ota_callback(false) -- Failed
        end
    else
        -- No callback provided, auto-restart on success
        if result == 0 then
            print("OTA update successful, restarting module...")
            if rtos and rtos.reboot then
                rtos.reboot()
            else
                print("No restart function available")
            end
        else
            print("OTA update failed:", error_msg)
        end
    end
end

-- Start OTA update process using libfota2
function ota_update.request(callback)
    print("OTA update request started (using libfota2)")

    -- Print firmware info for debugging
    local firmware_info = getFirmwareInfo()
    print("Current firmware configuration:")
    print("- PROJECT:", _G.PROJECT)
    print("- VERSION:", _G.VERSION)
    print("- PRODUCT_KEY:", _G.PRODUCT_KEY)
    print("- IMEI:", getIMEI())

    if ota_in_progress then
        print("OTA update already in progress")
        if callback then callback(false) end
        return false
    end

    -- Check if libfota2 is available
    if not libfota2_available or not libfota2 or not libfota2.request then
        print("libfota2 not available - cannot perform OTA update")
        if callback then callback(false) end
        return false
    end

    ota_in_progress = true
    ota_callback = callback

    print("Starting libfota2.request...")

    -- Wait for network to be ready, then start OTA request
    sys.taskInit(function()
        -- Wait for network to be ready
        sys.waitUntil("IP_READY", 30000)
        print("Network ready, starting OTA check...")

        -- Use libfota2.request with empty options (uses default IoT platform)
        local ota_opts = {}
        libfota2.request(libfota2_callback, ota_opts)
    end)

    return true
end

-- Get update message (placeholder for compatibility)
function ota_update.getUpdateMsg()
    return "OTA update using libfota2 (official Air780EG implementation)"
end

-- Check if OTA is in progress
function ota_update.isInProgress()
    return ota_in_progress
end

-- Test function to verify module functionality
function ota_update.test()
    print("=== OTA Module Test (libfota2) ===")
    print("Module loaded successfully")

    local firmware_info = getFirmwareInfo()

    print("Required functions check:")
    print("- libfota2 module:", libfota2_available and "✓" or "✗")
    print("- libfota2.request:", libfota2 and libfota2.request and "✓" or "✗")
    print("- mobile.imei:", mobile and mobile.imei and "✓" or "✗")

    if libfota2 then
        print("Available libfota2 functions:")
        for k, v in pairs(libfota2) do
            if type(v) == "function" then
                print("- libfota2." .. k)
            end
        end
    end

    print("=== Test Complete ===")
    return libfota2_available and libfota2 and libfota2.request
end

return ota_update
