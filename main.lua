-- main.lua - Air780EG Enhanced Firmware
-- <PERSON>a<PERSON><PERSON><PERSON> needs PROJECT and VERSION information
PROJECT = "mqtt_sensor_demo"
-- IoT platform requires xxx.yyy.zzz format version numbers
-- Note: Only xxx and zzz are used, yyy is ignored but must be present
-- Recommend keeping middle digit as 000
VERSION = "003.000.000"
PRODUCT_KEY = "AtqpMY7HfMCjxCgLw200LlNN2ZsiVszc"

-- Load core modules
local sys = require("sys")
local PinModule = require("pin_module")
local vars = require("variables")
local my_utils = require("my_utils")
local commands = require("commands")

-- Load optional modules
local update_available, update = pcall(require, "update")
if not update_available then
    local ota_available, ota_update = pcall(require, "ota_update")
    if ota_available then
        update = ota_update
        update_available = true
    else
        update = {request = function() return false end}
        update_available = false
    end
end

local mqtt_module_available, mqtt_module = pcall(require, "mqtt_module")
if not mqtt_module_available then
    mqtt_module = {
        init = function() return false end,
        publish = function() return false end,
        is_ready = function() return false end,
        get_client_id = function() return "unknown" end
    }
end

-- Configuration
local PRINT_INTERVAL = 5000
local mqtt_connection_beep_played = false
local ota_in_progress = false
local last_voltage = 0

-- Time tracking
sys.timerLoopStart(function() vars.currentTime = vars.currentTime + 1 end, 1000)

-- Sensor configs
local SHTC3 = {i2c_id = 0, addr = 0x70, cmd_wakeup = 0x3517, cmd_sleep = 0xB098,
               cmd_measure_normal = 0x7866, temp_offset = 0}
local GPS = require("libgnss_gps")
local ADC_CONFIG = {id = 1, channel = 0, scaling_factor = 16.14}

-- Simple logging
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO]", ...) end,
        warn = function(tag, ...) print("[WARN]", ...) end,
        error = function(tag, ...) print("[ERROR]", ...) end
    }
end

-- Simple JSON encoder
if not json then
    _G.json = {
        encode = function(obj)
            if type(obj) ~= "table" then return tostring(obj) end
            local res = "{"
            for k, v in pairs(obj) do
                res = res .. '"' .. k .. '":' .. (type(v) == "string" and '"' .. v .. '"' or tostring(v)) .. ","
            end
            return res:sub(1, -2) .. "}"
        end
    }
end

-- Initialize SHTC3 sensor
local function initSHTC3()
    local setup_result = i2c.setup(SHTC3.i2c_id, 100000)
    if setup_result ~= 1 then return false end

    local result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {0x00})
    if not result then return false end

    print("SHTC3 sensor initialized")
    return true
end

-- Sensor functions
local function readSHTC3()
    local wakeup_cmd_high = (SHTC3.cmd_wakeup >> 8) & 0xFF
    local wakeup_cmd_low = SHTC3.cmd_wakeup & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {wakeup_cmd_high, wakeup_cmd_low})
    sys.wait(1)

    local measure_cmd_high = (SHTC3.cmd_measure_normal >> 8) & 0xFF
    local measure_cmd_low = SHTC3.cmd_measure_normal & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {measure_cmd_high, measure_cmd_low})
    sys.wait(15)

    local data = i2c.recv(SHTC3.i2c_id, SHTC3.addr, 6)
    if not data or #data ~= 6 then return {temperature = 25.0, humidity = 50.0} end

    local temp_raw = (data:byte(1) << 8) | data:byte(2)
    local humidity_raw = (data:byte(4) << 8) | data:byte(5)
    local temperature = -45.0 + 175.0 * (temp_raw / 65535.0) + SHTC3.temp_offset
    local humidity = 100.0 * (humidity_raw / 65535.0)

    local sleep_cmd_high = (SHTC3.cmd_sleep >> 8) & 0xFF
    local sleep_cmd_low = SHTC3.cmd_sleep & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {sleep_cmd_high, sleep_cmd_low})

    return {temperature = temperature, humidity = humidity}
end

local function initGPS()
    if not GPS then return false end
    local success = GPS.init()
    if success then print("GPS initialized") end
    return success
end

local function getGPSPosition()
    return GPS and GPS.getPosition() or nil
end

local function initADC()
    if adc and adc.open then
        local result = adc.open(ADC_CONFIG.id, ADC_CONFIG.channel)
        if result == true or result == 1 then
            print("ADC initialized")
            return true
        end
    end
    print("ADC not available, using simulated voltage")
    return false
end

local function readVoltage()
    if adc and adc.read then
        local adcval, voltage_mv = adc.read(ADC_CONFIG.id, ADC_CONFIG.channel)
        if voltage_mv then
            return (voltage_mv * ADC_CONFIG.scaling_factor) / 1000.0 + (vars.voltage_offset or 0)
        end
    end
    -- Fallback: simulate voltage reading
    return 12.4 + (vars.voltage_offset or 0)
end

local function getRSSI()
    if mobile and mobile.signal then
        return mobile.signal() or 0
    elseif mobile and mobile.csq then
        return mobile.csq() or 0
    end
    return 0
end

-- Format sensor data as JSON
local function formatSensorData(temp, humidity, voltage, gps, rssi)
    local lat_str, lon_str = "0 N", "0 E"
    if gps and gps.latitude and gps.longitude then
        local lat = tonumber(gps.latitude) or 0
        local lon = tonumber(gps.longitude) or 0

        if lat ~= 0 and lon ~= 0 then
            local lat_deg = math.floor(lat / 100)
            local lat_min = lat - (lat_deg * 100)
            lat_str = string.format("%.5f N", lat_deg + (lat_min / 60))

            local lon_deg = math.floor(lon / 100)
            local lon_min = lon - (lon_deg * 100)
            lon_str = string.format("%.5f E", lon_deg + (lon_min / 60))
        end
    end

    -- Ensure all values are properly formatted and validated
    local safe_humidity = math.floor((tonumber(humidity) or 29) + 0.5)
    local safe_rssi = math.floor(tonumber(rssi) or 0)
    local safe_voltage = tonumber(voltage) or 10.0
    local safe_speed = math.floor(tonumber((gps and gps.speed) or 0) or 0)
    local safe_temp = math.floor((tonumber(temp) or 30) + 0.5)
    local safe_version = tostring(VERSION or "002.000.000")

    -- Ensure all string values are safe
    local safe_lon = tostring(lon_str or "0 E")
    local safe_lat = tostring(lat_str or "0 N")

    return string.format('{"Lon":"%s","Lat":"%s","hum":%d,"ver":"%s","rssi":%d,"volt":%.3f,"Speed":%d,"temp":%d,"motion":0,"light":0}',
        safe_lon, safe_lat, safe_humidity, safe_version, safe_rssi, safe_voltage, safe_speed, safe_temp)
end

-- Consolidated command functions
local function playBeep(pattern)
    if vars.sound_flag then
        sys.taskInit(function()
            if pattern == "check" then PinModule.beepPattern(1, 300, 0, 2000)
            elseif pattern == "lock" then PinModule.beepPattern(2, 200, 200, 2500)
            elseif pattern == "unlock" then PinModule.beepPattern(3, 150, 150, 3000)
            elseif pattern == "as" then PinModule.beepPattern(1, 500, 0, 1800)
            else PinModule.beepPattern(1, 200, 0, 2000) end
        end)
    end
end

-- Use commands module directly

-- Simplified SMS functions
local function sendSms(phoneNumber, message)
    if sms then
        -- Remove + prefix if present, as some SMS libraries handle international format differently
        if string.match(phoneNumber, "^%+") then
            phoneNumber = string.sub(phoneNumber, 2)  -- Remove the + prefix
        end
        return sms.send(phoneNumber, message)
    end
    return false
end

local function smsCallback(num, data, datetime)
    local original_num = num  -- Store the original full number for sending replies
    local short_num = string.sub(num, -8)  -- Extract last 8 digits for comparison

    -- Forward special keywords to MQTT
    local keywords = {"Tand", "TG", "hugatsaa", "kod", "code", "opt"}
    for _, keyword in ipairs(keywords) do
        if string.find(data, keyword) then
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"sms":"' .. data .. '"}')
            end
            return
        end
    end

    -- Handle restart command
    if string.lower(data) == "restart" then
        sendSms(original_num, "System restarting...")
        sys.taskInit(function()
            sys.wait(2000)
            if rtos and rtos.reboot then rtos.reboot() end
        end)
        return
    end

    -- Handle Air720-style phone number configuration
    for i = 1, 3 do
        local pattern = "x123456x" .. i
        if string.find(data, pattern) then
            local start_pos = string.find(data, pattern) + string.len(pattern)
            local end_pos = string.find(data, "xx") - 1
            if start_pos and end_pos and end_pos >= start_pos then
                local phone_num = string.sub(string.sub(data, start_pos, end_pos), -8)
                if string.len(phone_num) == 8 and my_utils.writeToFile("/data/phone" .. i .. ".txt", phone_num) then
                    vars["phone_number" .. i] = phone_num
                    sendSms(original_num, "success")
                else
                    sendSms(original_num, "fail")
                end
            else
                sendSms(original_num, "fail")
            end
            return
        end
    end

    -- Check authorization (compare using short numbers)
    if short_num ~= vars.phone_number1 and short_num ~= vars.phone_number2 and short_num ~= vars.phone_number3 then
        if string.len(short_num) >= 8 then
            sendSms(original_num, "taniulaagvi dugaar!")  -- Send to original full number
        end
        return
    end

    -- Store for processing (use original number for replies)
    vars.sms_data = data
    vars.callback_number = original_num
end

-- Initialize SMS
local function initSMS()
    if sms then
        sms.setNewSmsCb(smsCallback)
        print("SMS initialized")
        return true
    end
    return false
end

-- Task to read and print sensor data
local function sensorTask()
    print("Starting sensor reading task")

    -- Wait for system to stabilize
    sys.wait(2000)

    while true do
        -- Read sensor data
        local temp_hum = readSHTC3()
        local temperature = temp_hum and temp_hum.temperature or 25.0
        local humidity = temp_hum and temp_hum.humidity or 50.0
        local voltage = readVoltage() or 12.0
        local gps = getGPSPosition()
        local rssi = getRSSI() or 0

        -- Print compact sensor data with GPS details
        local gps_status = "NO"
        if gps then
            gps_status = string.format("OK(%.6f,%.6f,%.1fkm/h,sat:%d)",
                gps.latitude or 0, gps.longitude or 0, gps.speed or 0, gps.satellites or 0)
        elseif GPS and GPS.getSatelliteInfo then
            local sat_info = GPS.getSatelliteInfo()
            gps_status = string.format("NOFIX(sat:%d,q:%d)", sat_info.satellites or 0, sat_info.fix_quality or 0)
        end
        print("T:" .. string.format("%.1f", temperature) .. "°C H:" .. string.format("%.1f", humidity) .. "% V:" .. string.format("%.2f", voltage) .. "V RSSI:" .. rssi .. " GPS:" .. gps_status)



        -- Wait for the next reading interval
        sys.wait(PRINT_INTERVAL)
    end
end

-- Initialize mobile network
local function initMobile()
    if not mobile then return false end

    local timeout = 30
    local start_time = os.time()

    while os.time() - start_time < timeout do
        if mobile.status then
            local status = mobile.status()
            if status == "REGISTERED" or status == "REGISTERED_ROAMING" or
               status == 1 or status == 2 or status == 5 then
                print("Network registered, RSSI:", getRSSI())
                PinModule.setNetLED(1)
                return true
            end
        end
        sys.wait(1000)
    end

    local rssi = getRSSI()
    if rssi and rssi ~= 0 then
        print("Network timeout, signal detected")
        PinModule.setNetLED(1)
        return true
    end

    print("Network registration failed")
    return false
end

-- Simplified sensor data publishing
local function publishSensorData()
    sys.taskInit(function()
        if mqtt_module.is_ready() then
            local temp_hum = readSHTC3()
            local json_data = formatSensorData(temp_hum.temperature, temp_hum.humidity, readVoltage(), getGPSPosition(), getRSSI())
            mqtt_module.publish(json_data)
        end
    end)
end

-- Air720 compatible UART task
local function uartTask()
    local lastLockTime = 0
    local lastUnlockTime = 0
    local lastMirrorTime = 0
    local mirrorPressCount = 0
    local mirrorWindowStartTime = 0
    local mirrorPressWindow = 5  -- 5 second window for mirror presses
    local debounceTime = 2  -- 2 second debounce time
    local uart_initialized = false

    if not uart_initialized and uart then
        local uart_id = uart.exist and not uart.exist(1) and 0 or 1
        if gpio then pcall(gpio.setup, 18, nil, gpio.PULLUP) end
        uart_initialized = (uart.setup(uart_id, 9600, 8, 1, uart.NONE, uart.LSB, 1024) == 0)
    end

    while true do

        -- Read data from UART1 (simplified Air780 approach)
        local uart1_data = ""
        if uart and uart_initialized then
            local uart_id = 1
            if uart.exist and not uart.exist(1) then
                uart_id = 0
            end

            if uart.rxSize then
                local rx_size = uart.rxSize(uart_id)
                if rx_size and rx_size > 0 then
                    uart1_data = uart.read(uart_id, rx_size) or ""
                end
            end
        end

        -- Check if mirror press window has expired
        if mirrorPressCount > 0 and (vars.currentTime - mirrorWindowStartTime > mirrorPressWindow) then
            print("Mirror press window expired, resetting counter from " .. mirrorPressCount)
            mirrorPressCount = 0
        end

        if uart1_data and string.len(uart1_data) > 0 then
            -- Process each line in the received data
            for line in uart1_data:gmatch("[^\r\n]+") do
                line = line:match("^%s*(.-)%s*$")  -- Trim whitespace
                if string.len(line) > 8 then  -- Need at least 9 characters for identifier
                    print("Received UART1 Data: " .. line)

                    -- Process UART1 commands based on specific fixed identifiers (4, 8, 2, 1) - Air720 compatible
                    local identifier = line:sub(9, 9) -- Extracting the 9th character to identify the button/action
                    if identifier == "8" and (vars.currentTime - lastLockTime > debounceTime) then
                        print("Lock command detected and executed")
                        local success, err = pcall(lockCommand)
                        if not success then
                            print("Failed to execute lock command:", err or "Unknown error")
                        end
                        lastLockTime = vars.currentTime
                    elseif identifier == "4" and (vars.currentTime - lastUnlockTime > debounceTime) then
                        print("Unlock command received and executed")
                        local success, err = pcall(unlockCommand)
                        if not success then
                            print("Failed to execute unlock command:", err or "Unknown error")
                        end
                        lastUnlockTime = vars.currentTime
                    elseif identifier == "2" and (vars.currentTime - lastMirrorTime > debounceTime) then
                        print("Mirror command received")

                        -- Mirror press counting logic (Air720 compatible)
                        if mirrorPressCount == 0 then
                            mirrorWindowStartTime = vars.currentTime
                        end
                        mirrorPressCount = mirrorPressCount + 1
                        lastMirrorTime = vars.currentTime

                        print("Mirror press count: " .. mirrorPressCount)

                        -- Execute mirror command after 3 presses within window
                        if mirrorPressCount >= 3 then
                            print("Mirror command threshold reached, executing mirror command")
                            local success, err = pcall(commands.mirrorCommand)
                            if not success then
                                print("Failed to execute mirror command:", err or "Unknown error")
                            end
                            mirrorPressCount = 0  -- Reset counter
                        end
                    elseif identifier == "1" and (vars.currentTime - lastMirrorTime > debounceTime) then
                        print("Test command received and executed")
                        if vars.isLicensed then
                            local success, err = pcall(commands.testCommand)
                            if not success then
                                print("Failed to execute test command:", err or "Unknown error")
                            end
                        else
                            print("Device not licensed, Test command ignored")
                        end
                        lastMirrorTime = vars.currentTime
                    end
                end
            end
        end

        -- UART2 disabled for now to avoid conflicts and focus on UART1
        -- TODO: Re-enable UART2 once UART1 is working properly

        sys.wait(200) -- Delay 200ms (Air720 compatible)
    end
end

-- Simplified voltage monitoring (sensor data only)
local function monitorVoltage()
    local new_voltage = tonumber(readVoltage()) or 0
    if new_voltage == 0 then return end

    local voltage_diff = math.abs(new_voltage - last_voltage)
    if last_voltage > 0 and voltage_diff >= (vars.voltage_threshold or 0.5) and vars.voltage_notify_flag then
        sys.taskInit(function()
            if mqtt_module.is_ready() then
                publishSensorData()
            end
        end)
    end
    last_voltage = new_voltage
end

-- Simplified OTA functions
local function update_cb(result)
    ota_in_progress = false
    print("OTA update " .. (result and "success" or "failed"))
    if result and rtos and rtos.reboot then
        sys.taskInit(function() sys.wait(2000); rtos.reboot() end)
    end
end

local function startOTAUpdate()
    if ota_in_progress or not update_available then return false end
    print("Starting OTA update...")
    ota_in_progress = true
    sys.taskInit(function()
        sys.waitUntil("IP_READY", 30000)
        update.request(update_cb)
    end)
    return true
end

-- Simplified MQTT message handler
local function handleMQTTMessage(topic, payload)
    local success, data = pcall(json.decode, payload)
    if success and data and data.command then
        playBeep("check")

        if data.command == "check" then
            publishSensorData()
        elseif data.command == "lock" then
            commands.lockCommand(); publishSensorData()
        elseif data.command == "unlock" then
            commands.unlockCommand(); publishSensorData()
        elseif data.command == "as" then
            if vars.isLicensed then commands.asCommand() end
            publishSensorData()
        elseif data.command == "untar" then
            commands.untarCommand(); publishSensorData()
        elseif data.command == "update" then
            startOTAUpdate()
        elseif data.command == "restart" then
            print("Restart command received via MQTT")
            sys.taskInit(function()
                sys.wait(2000)
                if rtos and rtos.reboot then
                    rtos.reboot()
                end
            end)
        elseif data.command == "ota_debug" then
            print("OTA: PROJECT=" .. PROJECT .. " VERSION=" .. VERSION)
            if update and update.test then update.test() end
        elseif data.command == "geely_atlas_on" then
            -- Enable Geely Atlas mode
            commands.enableGeelyMode()
            print("Geely Atlas mode enabled via MQTT")
        elseif data.command == "geely_atlas_off" then
            -- Disable Geely Atlas mode
            commands.disableGeelyMode()
            print("Geely Atlas mode disabled via MQTT")
        elseif data.command == "geely_status" then
            -- Get Geely Atlas mode status
            local status = commands.getGeelyModeStatus()
            print("Geely Atlas mode status:", status and "enabled" or "disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish(string.format('{"geely_atlas_mode":%s}', status and "true" or "false"))
            end
        elseif data.command == "set_timing" then
            -- Set timing parameter: {"command":"set_timing","parameter":"lock_press","value":1500}
            local param = data.parameter
            local value = tonumber(data.value)
            if param and value and value > 0 then
                local success = commands.setTimingParameter(param, value)
                if success then
                    print(string.format("Timing parameter %s set to %d ms", param, value))
                else
                    print("Failed to set timing parameter:", param)
                end
            else
                print("Invalid timing parameter or value")
            end
        elseif data.command == "get_timing" then
            -- Get all timing parameters
            local params = commands.getTimingParameters()
            print("Current timing parameters:")
            for k, v in pairs(params) do
                print("  " .. k .. ": " .. v .. "ms")
            end
            if mqtt_module.is_ready() then
                local json_params = json.encode(params)
                mqtt_module.publish('{"timing_parameters":' .. json_params .. '}')
            end

        elseif data.command == "gpiotest" then
            sys.taskInit(function()
                local relays = {"Relay1", "Relay2", "Relay3", "KeyPower", "Key1", "Key2"}
                for _, relay in ipairs(relays) do
                    PinModule.relayControl(relay, 1); sys.wait(1000)
                    PinModule.relayControl(relay, 0); sys.wait(200)
                end
                playBeep("check")
            end)
        elseif data.command == "notify_on" then
            -- Enable voltage notifications (Air720 compatible JSON response)
            vars.voltage_notify_flag = true
            my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
            print("Voltage notification flag set to True")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notification flag set to True"}')
            end
        elseif data.command == "notify_off" then
            -- Disable voltage notifications (Air720 compatible JSON response)
            vars.voltage_notify_flag = false
            my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
            print("Voltage notification flag set to False")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notification flag set to False"}')
            end
        elseif data.command == "sound_on" then
            -- Enable key state (Air720 compatible JSON response)
            vars.key_state = true
            my_utils.saveConfig("key_state", vars.key_state)
            print("Key state flag set to True")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Key state flag set to True"}')
            end
        elseif data.command == "sound_off" then
            -- Disable key state (Air720 compatible JSON response)
            vars.key_state = false
            my_utils.saveConfig("key_state", vars.key_state)
            print("Key state flag set to False")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Key state flag set to False"}')
            end
        elseif data.command == "license" then
            -- Disable license (Air720 compatible)
            vars.isLicensed = false
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_disabled"}')
            end
        elseif data.command == "charged" or data.command == "license_enable" then
            -- Enable license (Air720 compatible)
            vars.isLicensed = true
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License enabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_enabled"}')
            end
        elseif data.command == "license_disable" then
            -- Disable license (Air720 compatible alternative command)
            vars.isLicensed = false
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_disabled"}')
            end
        elseif data.command == "license_status" then
            -- Get license status (Air720 compatible)
            local status = vars.isLicensed and "licensed" or "not_licensed"
            print("License status:", status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"license_status":"' .. status .. '"}')
            end
        elseif data.command == "volt_offset" then
            -- Set voltage offset (Air720 compatible JSON response)
            local offset = tonumber(data.value)
            if offset then
                vars.voltage_offset = offset
                my_utils.saveConfig("voltage_offset", vars.voltage_offset)
                print(string.format("Voltage offset set to %.2f", offset))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage offset set to %.2f"}', offset))
                end
            else
                print("Invalid voltage offset value")
            end
        elseif data.command == "volt_threshold" then
            -- Set voltage threshold (Air720 compatible JSON response)
            local threshold = tonumber(data.value)
            if threshold and threshold > 0 then
                vars.voltage_threshold = threshold
                my_utils.saveConfig("voltage_threshold", vars.voltage_threshold)
                print(string.format("Voltage threshold set to %.2f", threshold))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage threshold set to %.2f"}', threshold))
                end
            else
                print("Invalid voltage threshold value")
            end
        elseif data.command:sub(1, 4) == "volt" then
            local offset = tonumber(data.command:sub(5))
            if offset then
                vars.voltage_offset = offset
                my_utils.saveConfig("voltage_offset", vars.voltage_offset)
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage offset set to %.2f"}', offset))
                end
            end
        elseif data.command:sub(1, 2) == "th" then
            local threshold = tonumber(data.command:sub(3))
            if threshold and threshold > 0 then
                vars.voltage_threshold = threshold
                my_utils.saveConfig("voltage_threshold", vars.voltage_threshold)
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage threshold set to %.2f"}', threshold))
                end
            end
        elseif data.command and data.command:match("^unitel:%d%d%d%d%d%d%d%d %d+$") then
            local phoneNumber, unitAmount = data.command:match("^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
            unitAmount = tonumber(unitAmount)
            if unitAmount and unitAmount <= 2000 and sms and sms.send then
                local success = sms.send("1444", phoneNumber .. " " .. unitAmount)
                if mqtt_module.is_ready() then
                    if success then
                        mqtt_module.publish(string.format('{"status":"Success","message":"Sent Unitel command for %s with %s units"}', phoneNumber, unitAmount))
                    else
                        mqtt_module.publish('{"status":"Error","message":"Failed to send Unitel command"}')
                    end
                end
            elseif mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Error","message":"Invalid Unitel command"}')
            end

        end
    else
        -- Handle Air720-style string commands (non-JSON)
        local command = payload:lower()
        playBeep("check")

        if command == "check" then
            commands.checkCommand()
            publishSensorData()
        elseif command == "lock" then
            commands.lockCommand()
        elseif command == "unlock" then
            commands.unlockCommand()
        elseif command == "as" then
            commands.asCommand()
        elseif command == "untar" then
            commands.untarCommand()
        elseif command == "notify on" then
            -- Enable voltage notifications (Air720 format)
            vars.voltage_notify_flag = true
            my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
            print("Voltage notification flag set to True")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notifications enabled"}')
            end
        elseif command == "notify off" then
            -- Disable voltage notifications (Air720 format)
            vars.voltage_notify_flag = false
            my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
            print("Voltage notification flag set to False")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notifications disabled"}')
            end
        elseif command == "sound on" then
            -- Enable key state (Air720 format)
            vars.key_state = true
            my_utils.saveConfig("key_state", vars.key_state)
            print("Key state flag set to True")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Key state enabled"}')
            end
        elseif command == "sound off" then
            -- Disable key state (Air720 format)
            vars.key_state = false
            my_utils.saveConfig("key_state", vars.key_state)
            print("Key state flag set to False")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Key state disabled"}')
            end
        elseif command == "license" then
            -- Disable license (Air720 format)
            vars.isLicensed = false
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_disabled"}')
            end
        elseif command == "charged" or command == "license_enable" then
            -- Enable license (Air720 format)
            vars.isLicensed = true
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License enabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_enabled"}')
            end
        elseif command == "license_disable" then
            -- Disable license (Air720 format alternative command)
            vars.isLicensed = false
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_disabled"}')
            end
        elseif command == "license_status" then
            -- Get license status (Air720 format)
            local status = vars.isLicensed and "licensed" or "not_licensed"
            print("License status:", status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"license_status":"' .. status .. '"}')
            end
        elseif command:sub(1, 4) == "volt" then
            local offset = tonumber(command:sub(5))
            if offset then
                vars.voltage_offset = offset
                my_utils.saveConfig("voltage_offset", vars.voltage_offset)
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage offset set to %.2f"}', offset))
                end
            end
        elseif command:sub(1, 2) == "th" then
            local threshold = tonumber(command:sub(3))
            if threshold and threshold > 0 then
                vars.voltage_threshold = threshold
                my_utils.saveConfig("voltage_threshold", vars.voltage_threshold)
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage threshold set to %.2f"}', threshold))
                end
            end
        elseif command == "volt status" then
            -- Get voltage configuration status (Air720 format)
            local status = {
                current_voltage = readVoltage(),
                voltage_offset = vars.voltage_offset,
                voltage_threshold = vars.voltage_threshold,
                voltage_notify_flag = vars.voltage_notify_flag
            }
            print("Voltage Status:")
            print("  Current voltage: " .. string.format("%.2f", status.current_voltage) .. "V")
            print("  Voltage offset: " .. string.format("%.2f", status.voltage_offset))
            print("  Voltage threshold: " .. string.format("%.2f", status.voltage_threshold))
            print("  Notifications: " .. (status.voltage_notify_flag and "enabled" or "disabled"))
            if mqtt_module.is_ready() then
                local json_status = json.encode(status)
                mqtt_module.publish('{"voltage_status":' .. json_status .. '}')
            end
        elseif command == "geely atlas on" then
            -- Enable Geely Atlas mode (Air720 format)
            commands.enableGeelyMode()
            print("Geely Atlas mode enabled via MQTT")
        elseif command == "geely atlas off" then
            -- Disable Geely Atlas mode (Air720 format)
            commands.disableGeelyMode()
            print("Geely Atlas mode disabled via MQTT")
        elseif command:sub(1, 5) == "time:" then
            -- Air720 format: "time:param_name:value_ms"
            -- Example: "time:lock_press:1500"
            local parts = {}
            for part in string.gmatch(command, "[^:]+") do
                table.insert(parts, part)
            end

            if #parts == 3 then
                local param = parts[2]
                local value = tonumber(parts[3])
                if param and value and value > 0 then
                    local success = commands.setTimingParameter(param, value)
                    if success then
                        print(string.format("Timing parameter %s set to %d ms", param, value))
                    else
                        print("Failed to set timing parameter:", param)
                    end
                else
                    print("Invalid timing parameter or value")
                end
            else
                print("Invalid time command format. Use: time:param_name:value_ms")
            end
        elseif command:match("^unitel:%d%d%d%d%d%d%d%d %d+$") then
            local phoneNumber, unitAmount = command:match("^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
            unitAmount = tonumber(unitAmount)
            if unitAmount and unitAmount <= 2000 and sms and sms.send then
                local success = sms.send("1444", phoneNumber .. " " .. unitAmount)
                if mqtt_module.is_ready() then
                    if success then
                        mqtt_module.publish(string.format('{"status":"Success","message":"Sent Unitel command for %s with %s units"}', phoneNumber, unitAmount))
                    else
                        mqtt_module.publish('{"status":"Error","message":"Failed to send Unitel command"}')
                    end
                end
            elseif mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Error","message":"Invalid Unitel command"}')
            end
        else
            print("Unknown command:", command)
        end
    end
end

-- MQTT initial data publishing handler
local function handleInitialDataPublish()
    print("Publishing initial sensor data to MQTT")
    -- Call publishSensorData which will start a task
    if publishSensorData() then
        print("Started task to publish initial sensor data")
    else
        print("Failed to start task for publishing initial sensor data")
    end
end

-- Simplified SMS command processing with authorization
local function processSmsCommand(command, callback_number)
    -- Extract last 8 digits for authorization check
    local short_num = string.sub(callback_number, -8)

    -- Check if the number is authorized (compare using short numbers)
    local is_authorized = (short_num == vars.phone_number1 or
                          short_num == vars.phone_number2 or
                          short_num == vars.phone_number3)

    -- Commands that require authorization (Air720 compatible)
    local restricted_commands = {
        ["as"] = true,
        ["untar"] = true,
        ["lock"] = true,
        ["unlock"] = true
    }

    -- Check authorization for restricted commands
    if restricted_commands[command] and not is_authorized then
        sendSms(callback_number, "taniulaagvi dugaar!")  -- Unauthorized number message
        print("Unauthorized SMS command attempt from:", callback_number, "Command:", command)
        return
    end

    -- Check license for restricted commands (only if authorized)
    if not vars.isLicensed and restricted_commands[command] then
        sendSms(callback_number, "License expired")
        return
    end

    local commands_map = {
        check = function()
            -- Air720 compatible check response with formatted data and Google Maps link
            local temp_hum = readSHTC3()
            local voltage = readVoltage()
            local gps = getGPSPosition()

            local gpsMessage
            if gps and gps.lat and gps.lng and gps.lat ~= 0 and gps.lng ~= 0 then
                -- Create Google Maps link (Air720 compatible format)
                gpsMessage = string.format("https://maps.google.com/?q=%.6f,%.6f", gps.lat, gps.lng)
            else
                gpsMessage = "GPS signal lost"
            end

            local formattedMessage = string.format(
                "Tanii mashin:\n%s\nBatt: %.2fV | Temp: %.1fC\nHum: %.1f%%",
                gpsMessage, voltage, temp_hum.temperature, temp_hum.humidity
            )
            sendSms(callback_number, formattedMessage)
        end,
        lock = function() commands.lockCommand(); sendSms(callback_number, "locked") end,
        unlock = function() commands.unlockCommand(); sendSms(callback_number, "unlocked") end,
        as = function()
            -- Air720 compatible as response with voltage monitoring
            commands.asCommand()

            -- Wait and check voltage like Air720
            sys.taskInit(function()
                sys.wait(4000) -- Initial wait

                local max_tries = 5
                local voltage_threshold = 13.4
                local successful = false

                for i = 1, max_tries do
                    local voltage = readVoltage()
                    if voltage >= voltage_threshold then
                        successful = true
                        break
                    end
                    if i < max_tries then
                        sys.wait(4000)
                    end
                end

                local final_voltage = readVoltage()
                local temp_hum = readSHTC3()
                local status = final_voltage >= 13.4 and "aslaa" or "tulhuur taniagvi"

                local message = string.format("Tani mashin %s!\nBatt: %.2fV | Temp: %.1fC\nStatus: %s",
                    status, final_voltage, temp_hum.temperature, status)
                sendSms(callback_number, message)
            end)
        end,
        untar = function()
            -- Air720 compatible untar response with voltage monitoring
            commands.untarCommand()

            -- Wait and check voltage like Air720
            sys.taskInit(function()
                sys.wait(5000) -- Initial wait

                local max_tries = 3
                local voltage_threshold = 13.5
                local successful = false

                for i = 1, max_tries do
                    local voltage = readVoltage()
                    if voltage <= voltage_threshold then
                        successful = true
                        break
                    end
                    if i < max_tries then
                        sys.wait(5000)
                    end
                end

                local final_voltage = readVoltage()
                local temp_hum = readSHTC3()
                local status = final_voltage <= voltage_threshold and "untarlaa" or "check"

                local message = string.format("Tani mashin:\nBatt: %.2fV | Temp: %.1fC\nStatus: %s",
                    final_voltage, temp_hum.temperature, status)
                sendSms(callback_number, message)
            end)
        end,
        version = function() sendSms(callback_number, VERSION) end,
        ["notify on"] = function()
            vars.voltage_notify_flag = true
            my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
            sendSms(callback_number, "Voltage notifications enabled")
        end,
        ["notify off"] = function()
            vars.voltage_notify_flag = false
            my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
            sendSms(callback_number, "Voltage notifications disabled")
        end,
        ["sound on"] = function()
            vars.key_state = true
            my_utils.saveConfig("key_state", vars.key_state)
            sendSms(callback_number, "Key state enabled")
        end,
        ["sound off"] = function()
            vars.key_state = false
            my_utils.saveConfig("key_state", vars.key_state)
            sendSms(callback_number, "Key state disabled")
        end,
        license = function()
            vars.isLicensed = false
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            sendSms(callback_number, "License disabled")
        end,
        charged = function()
            vars.isLicensed = true
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            sendSms(callback_number, "License enabled")
        end,
        ["volt status"] = function()
            local voltage = readVoltage()
            sendSms(callback_number, string.format("Volt: %.2fV | Offset: %.2f | Threshold: %.2f | Notify: %s",
                voltage, vars.voltage_offset, vars.voltage_threshold, vars.voltage_notify_flag and "ON" or "OFF"))
        end
    }

    if commands_map[command] then
        commands_map[command]()
    elseif command:sub(1, 4) == "volt" then
        -- Voltage commands require authorization
        if not is_authorized then
            sendSms(callback_number, "taniulaagvi dugaar!")
            return
        end
        local offset = tonumber(command:sub(5))
        if offset then
            vars.voltage_offset = offset
            my_utils.saveConfig("voltage_offset", vars.voltage_offset)
            sendSms(callback_number, string.format("Voltage offset set to %.2f", offset))
        end
    elseif command:sub(1, 2) == "th" then
        -- Threshold commands require authorization
        if not is_authorized then
            sendSms(callback_number, "taniulaagvi dugaar!")
            return
        end
        local threshold = tonumber(command:sub(3))
        if threshold and threshold > 0 then
            vars.voltage_threshold = threshold
            my_utils.saveConfig("voltage_threshold", vars.voltage_threshold)
            sendSms(callback_number, string.format("Voltage threshold set to %.2f", threshold))
        end
    elseif command:match("^unitel:%d%d%d%d%d%d%d%d %d+$") then
        -- Unitel command requires authorization
        if not is_authorized then
            sendSms(callback_number, "taniulaagvi dugaar!")
            return
        end
        -- Handle unitel command via SMS: "unitel:88889999 1000"
        local phoneNumber, unitAmount = command:match("^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
        unitAmount = tonumber(unitAmount)

        if unitAmount and unitAmount <= 2000 then
            -- Send the command to the unitel service (1444)
            local success = sms.send("1444", phoneNumber .. " " .. unitAmount)
            if success then
                sendSms(callback_number, string.format("Unitel: %s units sent to %s", unitAmount, phoneNumber))
            else
                sendSms(callback_number, "Failed to send Unitel command")
            end
        else
            sendSms(callback_number, "Invalid Unitel: Phone 8 digits, amount <= 2000")
        end
    elseif command == "notify on" or command == "notify off" then
        -- Notify commands require authorization
        if not is_authorized then
            sendSms(callback_number, "taniulaagvi dugaar!")
            return
        end
        -- These are handled in commands_map above, but adding explicit check for clarity
    end
end

-- Simplified initialization
local function initTask()
    print("Air780EG v" .. VERSION)

    PinModule.setupPins()
    initSHTC3(); initADC(); initGPS(); initSMS()
    my_utils.createDirectory("/data")
    my_utils.loadConfiguration()
    initMobile()
    mqtt_module.init()

    sys.subscribe("MQTT_MESSAGE_RECEIVED", handleMQTTMessage)
    sys.subscribe("MQTT_CONNECTED", function() PinModule.setCloudLED(1); playBeep("check") end)
    sys.subscribe("MQTT_DISCONNECTED", function() PinModule.setCloudLED(0) end)

    sys.taskInit(sensorTask)
    -- sys.taskInit(uartTask)  -- Temporarily disabled to test GPS functionality
    sys.timerLoopStart(monitorVoltage, 10000)

    -- Network monitoring
    sys.taskInit(function()
        while true do
            sys.wait(5000)
            if mobile and mobile.status then
                local status = mobile.status()
                PinModule.setNetLED((status == "REGISTERED" or status == "REGISTERED_ROAMING" or
                                   status == 1 or status == 2 or status == 5 or getRSSI() ~= 0) and 1 or 0)
            end
        end
    end)

    -- SMS processing
    sys.taskInit(function()
        while true do
            if vars.sms_data then
                processSmsCommand(vars.sms_data, vars.callback_number)
                vars.sms_data, vars.callback_number = nil, nil
            end
            sys.wait(1000)
        end
    end)

    print("Ready! Auth:", vars.phone_number1 or "none")
end

-- Main application entry point
local function main()
    -- Start the initialization task in a coroutine
    sys.taskInit(initTask)

    -- Keep the system running
    sys.run()
end

-- Start the application
main()
